import {
	DeviceInputSchema,
	DeviceModelSchema,
} from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const DeviceListSchema = createListOutputSchema(DeviceModelSchema);
export const devices = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.device.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(DeviceListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.device, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.device.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.input(DeviceInputSchema)
		.handler(async ({ input }) => {
			// TODO: Hash device ID
			const salt = crypto.randomUUID();
			const deviceHash = crypto.randomUUID();
			return await prisma.device.create({
				data: {
					licenseId: input.licenseId,
					appVersion: input.appVersion,
					deviceHash,
					salt,
					deviceName: input.deviceName,
					deviceType: input.deviceType,
					deviceModel: input.deviceModel,
					operatingSystem: input.operatingSystem,
					architecture: input.architecture,
					screenResolution: input.screenResolution,
					totalMemory: input.totalMemory,
					userNickname: input.userNickname,
					location: input.location,
					notes: input.notes,
				},
			});
		}),

	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				deviceName: z.string().optional(),
				deviceType: z.string().optional(),
				deviceModel: z.string().optional(),
				operatingSystem: z.string().optional(),
				architecture: z.string().optional(),
				screenResolution: z.string().optional(),
				totalMemory: z.string().optional(),
				userNickname: z.string().optional(),
				location: z.string().optional(),
				notes: z.string().optional(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.device.update({
				where: {
					id: input.id,
				},
				data: {
					deviceName: input.deviceName,
					deviceType: input.deviceType,
					deviceModel: input.deviceModel,
					operatingSystem: input.operatingSystem,
					architecture: input.architecture,
					screenResolution: input.screenResolution,
					totalMemory: input.totalMemory,
					userNickname: input.userNickname,
					location: input.location,
					notes: input.notes,
				},
			});
		}),
	delete: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.device.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
