import { DeviceExpansionStatus } from "prisma/generated/enums";
import {
	DeviceExpansionInputSchema,
	DeviceExpansionModelSchema,
} from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const DeviceExpansionListSchema = createListOutputSchema(
	DeviceExpansionModelSchema,
);
export const expansions = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.deviceExpansion.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(DeviceExpansionListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.deviceExpansion, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
	create: protectedProcedure
		.input(DeviceExpansionInputSchema)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.create({
				data: {
					licenseId: input.licenseId,
					paymentIntentId: input.paymentIntentId,
					additionalDevices: input.additionalDevices,
					amount: input.amount,
				},
			});
		}),

	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				status: z.enum(DeviceExpansionStatus).optional(),
				additionalDevices: z.number().int().min(1).optional(),
				amount: z.number().int().min(1).optional(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
					additionalDevices: input.additionalDevices,
					amount: input.amount,
				},
			});
		}),
	delete: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
