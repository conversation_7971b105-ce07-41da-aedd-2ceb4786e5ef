import { InvitationStatus } from "prisma/generated/enums";
import {
	UserInvitationInputSchema,
	UserInvitationModelSchema,
} from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const UserInvitationListSchema = createListOutputSchema(
	UserInvitationModelSchema,
);
export const invitations = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.userInvitation.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(UserInvitationListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.userInvitation, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.input(UserInvitationInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.create({
				data: {
					email: input.email,
					role: input.role,
					sentBy: input.sentBy,
					token: input.token,
					expiresAt: input.expiresAt,
				},
			});
		}),

	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				status: z.enum(InvitationStatus).optional(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
				},
			});
		}),

	delete: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
