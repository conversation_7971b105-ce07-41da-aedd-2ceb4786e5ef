import { PaymentIntentModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const PaymentIntentListSchema = createListOutputSchema(
	PaymentIntentModelSchema,
);
export const payments = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.paymentIntent.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(PaymentIntentListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.paymentIntent, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.paymentIntent.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};
