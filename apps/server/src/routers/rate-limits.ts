import {
	RateLimitModelSchema,
	RefundRequestModelSchema,
} from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const RateLimitListSchema = createListOutputSchema(RateLimitModelSchema);
export const rateLimits = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.rateLimit.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(RateLimitListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.rateLimit, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.rateLimit.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};
