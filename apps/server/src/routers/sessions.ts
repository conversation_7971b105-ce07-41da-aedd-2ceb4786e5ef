import { SessionModelSchema } from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const SessionListSchema = createListOutputSchema(SessionModelSchema);
export const sessions = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.session.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(SessionListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.session, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.session.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
	delete: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.session.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
