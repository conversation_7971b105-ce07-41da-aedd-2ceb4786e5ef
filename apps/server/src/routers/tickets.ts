import { TicketPriority, TicketStatus } from "prisma/generated/enums";
import {
	SupportTicketInputSchema,
	SupportTicketModelSchema,
} from "prisma/generated/zod/schemas";
import { z } from "zod";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildListResponse, createListOutputSchema } from "@/lib/pagination";
import { paginationSchema } from "@/types";

const SupportTicketListSchema = createListOutputSchema(
	SupportTicketModelSchema,
);
export const tickets = {
	list: protectedProcedure.handler(async ({ context }) => {
		return await prisma.supportTicket.findMany();
	}),
	paginate: protectedProcedure
		.input(paginationSchema)
		.output(SupportTicketListSchema)
		.handler(async ({ input }) => {
			return await buildListResponse(prisma.supportTicket, input);
		}),

	find: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.supportTicket.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
	create: protectedProcedure
		.input(SupportTicketInputSchema)
		.handler(async ({ input }) => {
			function generateTicketId() {
				const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
				let result = "SNAP-";
				const year = new Date().getFullYear().toString().slice(-2);
				result += `${year}-`;
				for (let i = 0; i < 3; i++) {
					result += characters.charAt(
						Math.floor(Math.random() * characters.length),
					);
				}
				return result;
			}
			const ticketId = generateTicketId();
			return await prisma.supportTicket.create({
				data: {
					customerEmail: input.customerEmail,
					customerName: input.customerName,
					licenseKey: input.licenseKey,
					subject: input.subject,
					description: input.description,
					category: input.category,
					priority: input.priority,
					status: input.status,
					assignedTo: input.assignedTo,
					ticketId: ticketId,
				},
			});
		}),
	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				priority: z.enum(TicketPriority).optional(),
				status: z.enum(TicketStatus).optional(),
				assignedTo: z.string().optional(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.supportTicket.update({
				where: {
					id: input.id,
				},
				data: {
					priority: input.priority,
					status: input.status,
					assignedTo: input.assignedTo,
				},
			});
		}),

	delete: protectedProcedure
		.input(
			z.object({
				id: z.string(),
			}),
		)
		.handler(async ({ input }) => {
			return await prisma.supportTicket.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
