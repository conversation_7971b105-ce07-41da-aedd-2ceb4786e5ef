import type { User } from "@snapback/shared";
import { createOptimisticListActions } from "@/utils/createOptimisticActions";
import { orpc } from "@/utils/orpc";

export const useUserActions = (
	params: { page?: number; limit?: number } = {},
) =>
	createOptimisticListActions<User>(
		[
			...orpc.users.paginate.queryOptions({
				input: { page: params.page ?? 1, limit: params.limit ?? 10 },
			}).queryKey,
		],
		{
			update: async (variables) => {
				// Map the generic User type to the specific update input type
				const updateInput = {
					id: variables.id,
					role: variables.role as
						| "SUPER_ADMIN"
						| "ADMIN"
						| "COLLABORATOR"
						| "USER"
						| "VIWER"
						| undefined,
					isActive: variables.isActive as boolean | undefined,
				};
				// Use the mutation function from ORPC mutation options
				const mutationFn = orpc.users.update.mutationOptions().mutationFn;
				if (!mutationFn) {
					throw new Error("Update mutation function not available");
				}
				return await mutationFn(updateInput);
			},
			delete: async (id) => {
				// Use the mutation function from ORPC mutation options
				const mutationFn = orpc.users.delete.mutationOptions().mutationFn;
				if (!mutationFn) {
					throw new Error("Delete mutation function not available");
				}
				await mutationFn({ id });
			},
			create: async (variables) => {
				// Map the generic User type to the specific create input type
				// The server only uses name, email, and role from UserInputSchema
				const createInput = {
					name: variables.name,
					email: variables.email,
					emailVerified: false, // Default value
					role: variables.role,
					isActive: true, // Default value
					sessions: [],
					accounts: [],
					createdLicenses: [],
					sentInvitations: [],
					receivedInvitations: [],
					auditLogsAsActor: [],
					auditLogsAsTarget: [],
					processedRefunds: [],
					assignedTickets: [],
					supportMessages: [],
				};
				// Use the mutation function from ORPC mutation options
				const mutationFn = orpc.users.create.mutationOptions().mutationFn;
				if (!mutationFn) {
					throw new Error("Create mutation function not available");
				}
				return await mutationFn(createInput);
			},
		},
	);
