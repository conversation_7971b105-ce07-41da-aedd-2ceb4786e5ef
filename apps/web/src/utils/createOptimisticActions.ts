import { useOptimisticMutation } from "@/hooks/useOptimisticMutation";

export function createOptimisticListActions<TItem extends { id: string }>(
	listQueryKey: unknown[],
	mutations: {
		update: (variables: Partial<TItem> & { id: string }) => Promise<TItem>;
		delete: (id: string) => Promise<void>;
		create: (variables: Omit<TItem, "id">) => Promise<TItem>;
	},
) {
	return () => {
		const update = useOptimisticMutation({
			queryKey: listQueryKey,
			mutationFn: mutations.update,
			updateFn: (oldData: any, variables) => {
				if (!oldData?.items) return oldData;
				return {
					...oldData,
					items: oldData.items.map((item: TItem) =>
						item.id === variables.id ? { ...item, ...variables } : item,
					),
				};
			},
		});

		const deleteItem = useOptimisticMutation({
			queryKey: listQueryKey,
			mutationFn: mutations.delete,
			updateFn: (oldData: any, id) => {
				if (!oldData?.items) return oldData;
				return {
					...oldData,
					items: oldData.items.filter((item: TItem) => item.id !== id),
				};
			},
		});

		const create = useOptimisticMutation({
			queryKey: listQueryKey,
			mutationFn: mutations.create,
			updateFn: (oldData: any, variables) => {
				if (!oldData?.items) return oldData;
				const optimisticItem = { ...variables, id: `temp-${Date.now()}` };
				return {
					...oldData,
					items: [optimisticItem, ...oldData.items],
				};
			},
		});

		return { update, delete: deleteItem, create };
	};
}
