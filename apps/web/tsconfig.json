{"compilerOptions": {"strict": true, "esModuleInterop": true, "jsx": "react-jsx", "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "verbatimModuleSyntax": true, "skipLibCheck": true, "types": ["vite/client"], "rootDirs": ["."], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@snapback/shared/*": ["../../packages/shared/src/*"]}}, "references": [{"path": "../server"}, {"path": "../../packages/shared"}]}