{"$schema": "https://biomejs.dev/schemas/2.2.0/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/.next", "!**/dist", "!**/.turbo", "!**/dev-dist", "!**/.zed", "!**/.vscode", "!**/routeTree.gen.ts", "!**/src-tauri", "!**/.nuxt", "!bts.jsonc", "!**/.expo", "!**/.wrangler", "!**/.alchemy", "!**/wrangler.jsonc", "!**/.source", "!**/node_modules", "!**/generated/*"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"useExhaustiveDependencies": "info"}, "nursery": {"useSortedClasses": {"level": "warn", "fix": "safe", "options": {"functions": ["clsx", "cva", "cn"]}}}, "suspicious": {"noDoubleEquals": "error", "noExplicitAny": "warn", "noGlobalIsFinite": "error", "noUnknownAtRules": "warn"}, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}