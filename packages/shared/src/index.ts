/**
 * @snapback/shared
 *
 * Shared types, utilities, and Prisma client exports for the Snapback License System monorepo.
 *
 * This package provides centralized access to:
 * - Prisma client and all generated types
 * - Database enums and models
 * - Zod validation schemas
 * - Common utilities and types
 */

// Re-export all Prisma types and client
export * from "./prisma";
// You can add other shared utilities, constants, or types here
// For example:
// export * from './utils'
// export * from './constants'
// export * from './types'
