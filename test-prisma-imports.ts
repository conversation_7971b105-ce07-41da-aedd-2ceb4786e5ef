/**
 * Test file to verify Prisma type imports from @snapback/shared
 * This file should be deleted after testing is complete.
 */

// Test importing from the main shared package
import { 
  PrismaClient, 
  User, 
  License, 
  UserRole, 
  LicenseStatus,
  Device,
  DeviceStatus 
} from '@snapback/shared'

// Test importing from the specific prisma export
import { 
  PrismaClient as PrismaClient2,
  Prisma,
  UserRole as UserRole2 
} from '@snapback/shared/prisma'

// Test type usage
const testUser: User = {
  id: 'test-id',
  name: 'Test User',
  email: '<EMAIL>',
  emailVerified: false,
  image: null,
  role: UserRole.USER,
  isActive: true,
  invitedBy: null,
  invitedAt: null,
  lastLoginAt: null,
  createdAt: new Date(),
  updatedAt: new Date()
}

const testLicense: License = {
  id: 'license-id',
  licenseKey: 'test-key',
  customerEmail: '<EMAIL>',
  customerName: 'Customer Name',
  licenseType: 'PRO' as any, // This should be LicenseType.PRO
  status: LicenseStatus.ACTIVE,
  maxDevices: 5,
  currentDevices: 0,
  issuedAt: new Date(),
  expiresAt: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: 'user-id',
  emailDeliveryStatus: 'DELIVERED' as any,
  lastEmailSentAt: null,
  emailRetryCount: 0,
  notes: null,
  metadata: null
}

// Test enum usage
const userRoles: UserRole[] = [
  UserRole.USER,
  UserRole.ADMIN,
  UserRole.COLLABORATOR,
  UserRole.SUPER_ADMIN
]

const licenseStatuses: LicenseStatus[] = [
  LicenseStatus.ACTIVE,
  LicenseStatus.EXPIRED,
  LicenseStatus.SUSPENDED,
  LicenseStatus.REFUNDED,
  LicenseStatus.CANCELLED
]

console.log('Prisma types imported successfully!')
console.log('Test user:', testUser)
console.log('Test license:', testLicense)
console.log('User roles:', userRoles)
console.log('License statuses:', licenseStatuses)
